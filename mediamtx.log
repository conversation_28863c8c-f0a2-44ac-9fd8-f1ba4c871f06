2025/08/01 11:42:13 INF MediaMTX v1.5.1
2025/08/01 11:42:13 INF configuration loaded from /home/<USER>/project/fault_detect/mediamtx_test.yml
2025/08/01 11:42:13 INF [metrics] listener opened on 127.0.0.1:9998
2025/08/01 11:42:13 INF [RTSP] listener opened on 192.168.11.128:8554 (TCP), :8000 (UDP/RTP), :8001 (UDP/RTCP)
2025/08/01 11:42:13 INF [RTMP] listener opened on :1935
2025/08/01 11:42:13 INF [HLS] listener opened on :8888
2025/08/01 11:42:13 INF [WebRTC] listener opened on :8889 (HTTP), :8189 (ICE/UDP)
2025/08/01 11:42:13 INF [SRT] listener opened on :8890 (UDP)
2025/08/01 11:42:13 INF [API] listener opened on 127.0.0.1:9997
2025/08/01 11:45:00 INF [RTSP] [conn 192.168.11.128:33156] opened
2025/08/01 11:45:00 INF [RTSP] [session 975fcc64] created by 192.168.11.128:33156
2025/08/01 11:45:00 INF [RTSP] [session 975fcc64] is publishing to path 'live', 2 tracks (H264, MPEG-4 Audio)
2025/08/01 11:45:16 INF [RTSP] [session 975fcc64] destroyed: torn down by 192.168.11.128:33156
2025/08/01 11:45:16 INF [RTSP] [conn 192.168.11.128:33156] closed: EOF
2025/08/01 11:45:31 INF [RTSP] [conn 192.168.11.128:57482] opened
2025/08/01 11:45:31 INF [RTSP] [session 6f8d5ad6] created by 192.168.11.128:57482
2025/08/01 11:45:31 INF [RTSP] [session 6f8d5ad6] is publishing to path 'live', 2 tracks (H264, MPEG-4 Audio)
2025/08/01 11:45:49 INF [RTSP] [conn 192.168.11.1:54001] opened
2025/08/01 11:45:49 INF [RTSP] [session 07caaee5] created by 192.168.11.1:54001
2025/08/01 11:45:49 INF [RTSP] [session 07caaee5] is reading from path 'live', with UDP, 2 tracks (H264, MPEG-4 Audio)
2025/08/01 11:45:49 WAR [RTSP] [session 07caaee5] rtcp: invalid packet version
2025/08/01 11:46:26 INF [RTSP] [conn 192.168.11.128:33972] opened
2025/08/01 11:46:26 INF [RTSP] [session a7c72e99] created by 192.168.11.128:33972
2025/08/01 11:46:26 INF [RTSP] [session a7c72e99] destroyed: not in use
2025/08/01 11:46:26 INF [RTSP] [conn 192.168.11.128:33972] closed: path of a SETUP request must end with a slash. This typically happens when VLC fails a request, and then switches to an unsupported RTSP dialect
2025/08/01 11:46:26 INF [RTSP] [conn 192.168.11.128:33988] opened
2025/08/01 11:46:26 INF [RTSP] [conn 192.168.11.128:33988] closed: EOF
2025/08/01 11:46:34 INF [RTSP] [conn 192.168.11.128:49202] opened
2025/08/01 11:46:34 INF [RTSP] [session fb01696b] created by 192.168.11.128:49202
2025/08/01 11:46:34 INF [RTSP] [conn 192.168.11.128:49202] closed: path of a SETUP request must end with a slash. This typically happens when VLC fails a request, and then switches to an unsupported RTSP dialect
2025/08/01 11:46:34 INF [RTSP] [session fb01696b] destroyed: not in use
2025/08/01 11:46:34 INF [RTSP] [conn 192.168.11.128:49216] opened
2025/08/01 11:46:34 INF [RTSP] [conn 192.168.11.128:49216] closed: EOF
2025/08/01 11:46:38 INF [RTSP] [session 07caaee5] destroyed: torn down by 192.168.11.1:54001
2025/08/01 11:46:38 INF [RTSP] [conn 192.168.11.1:54001] closed: EOF
2025/08/01 11:46:43 INF [RTSP] [conn 192.168.11.128:35752] opened
2025/08/01 11:46:43 INF [RTSP] [session 359d0442] created by 192.168.11.128:35752
2025/08/01 11:46:43 INF [RTSP] [session 359d0442] destroyed: not in use
2025/08/01 11:46:43 INF [RTSP] [conn 192.168.11.128:35752] closed: path of a SETUP request must end with a slash. This typically happens when VLC fails a request, and then switches to an unsupported RTSP dialect
2025/08/01 11:46:43 INF [RTSP] [conn 192.168.11.128:35756] opened
2025/08/01 11:46:43 INF [RTSP] [conn 192.168.11.128:35756] closed: EOF
2025/08/01 11:47:19 INF [RTSP] [conn 192.168.11.128:44130] opened
2025/08/01 11:47:19 INF [RTSP] [session 755eeb3b] created by 192.168.11.128:44130
2025/08/01 11:47:19 INF [RTSP] [conn 192.168.11.128:44130] closed: path of a SETUP request must end with a slash. This typically happens when VLC fails a request, and then switches to an unsupported RTSP dialect
2025/08/01 11:47:19 INF [RTSP] [session 755eeb3b] destroyed: not in use
2025/08/01 11:47:19 INF [RTSP] [conn 192.168.11.128:44142] opened
2025/08/01 11:47:19 INF [RTSP] [conn 192.168.11.128:44142] closed: EOF
2025/08/01 11:47:51 INF [RTSP] [conn 192.168.11.128:43818] opened
2025/08/01 11:47:51 INF [RTSP] [session c178455f] created by 192.168.11.128:43818
2025/08/01 11:47:51 INF [RTSP] [session c178455f] destroyed: not in use
2025/08/01 11:47:51 INF [RTSP] [conn 192.168.11.128:43818] closed: path has changed, was '/live', now is '/stream=0'
2025/08/01 11:47:51 INF [RTSP] [conn 192.168.11.128:43834] opened
2025/08/01 11:47:51 INF [RTSP] [conn 192.168.11.128:43834] closed: EOF
2025/08/01 11:48:17 INF [RTSP] [conn 192.168.11.128:51730] opened
2025/08/01 11:48:17 INF [RTSP] [session a4c06767] created by 192.168.11.128:51730
2025/08/01 11:48:17 INF [RTSP] [conn 192.168.11.128:51730] closed: path of a SETUP request must end with a slash. This typically happens when VLC fails a request, and then switches to an unsupported RTSP dialect
2025/08/01 11:48:17 INF [RTSP] [session a4c06767] destroyed: not in use
2025/08/01 11:48:17 INF [RTSP] [conn 192.168.11.128:51734] opened
2025/08/01 11:48:17 INF [RTSP] [conn 192.168.11.128:51734] closed: EOF
2025/08/01 11:48:43 INF [RTSP] [session 6f8d5ad6] destroyed: torn down by 192.168.11.128:57482
2025/08/01 11:48:43 INF [RTSP] [conn 192.168.11.128:57482] closed: read tcp 192.168.11.128:8554->192.168.11.128:57482: read: connection reset by peer
2025/08/01 11:50:02 INF [RTSP] [conn 192.168.11.128:36538] opened
2025/08/01 11:50:03 INF [RTSP] [session 2b86f1f1] created by 192.168.11.128:36538
2025/08/01 11:50:03 INF [RTSP] [session 2b86f1f1] is publishing to path 'live', 1 track (H264)
2025/08/01 11:50:26 INF [RTSP] [session 2b86f1f1] destroyed: torn down by 192.168.11.128:36538
2025/08/01 11:50:26 INF [RTSP] [conn 192.168.11.128:36538] closed: EOF
2025/08/01 11:52:56 INF [RTSP] [conn 192.168.11.128:33580] opened
2025/08/01 11:52:56 INF [RTSP] [session a4cf96ed] created by 192.168.11.128:33580
2025/08/01 11:52:56 INF [RTSP] [session a4cf96ed] is publishing to path 'live', 1 track (H264)
2025/08/01 11:52:58 INF [RTSP] [session a4cf96ed] destroyed: torn down by 192.168.11.128:33580
2025/08/01 11:52:58 INF [RTSP] [conn 192.168.11.128:33580] closed: read tcp 192.168.11.128:8554->192.168.11.128:33580: read: connection reset by peer
2025/08/01 11:53:05 INF [RTSP] [conn 192.168.11.128:38186] opened
2025/08/01 11:53:05 INF [RTSP] [session b23cfb59] created by 192.168.11.128:38186
2025/08/01 11:53:05 INF [RTSP] [session b23cfb59] is publishing to path 'live', 1 track (H264)
2025/08/01 11:53:20 INF [RTSP] [session b23cfb59] destroyed: torn down by 192.168.11.128:38186
2025/08/01 11:53:20 INF [RTSP] [conn 192.168.11.128:38186] closed: EOF
2025/08/01 11:53:43 INF [RTSP] [conn 192.168.11.128:45080] opened
2025/08/01 11:53:43 INF [RTSP] [session 89e55819] created by 192.168.11.128:45080
2025/08/01 11:53:43 INF [RTSP] [session 89e55819] is publishing to path 'live', 1 track (H264)
2025/08/01 11:53:48 INF [RTSP] [conn 192.168.11.1:54281] opened
2025/08/01 11:53:48 INF [RTSP] [session 7b29f0ba] created by 192.168.11.1:54281
2025/08/01 11:53:48 INF [RTSP] [session 7b29f0ba] is reading from path 'live', with UDP, 1 track (H264)
2025/08/01 11:53:53 INF [RTSP] [session 89e55819] destroyed: torn down by 192.168.11.128:45080
2025/08/01 11:53:53 INF [RTSP] [conn 192.168.11.1:54281] closed: terminated
2025/08/01 11:53:53 INF [RTSP] [session 7b29f0ba] destroyed: terminated
2025/08/01 11:53:53 INF [RTSP] [conn 192.168.11.128:45080] closed: EOF
2025/08/01 13:53:40 INF [RTSP] [conn 192.168.11.128:56752] opened
2025/08/01 13:53:40 INF [RTSP] [session edd9c9fc] created by 192.168.11.128:56752
2025/08/01 13:53:40 INF [RTSP] [session edd9c9fc] is publishing to path 'live', 1 track (H264)
2025/08/01 13:53:43 INF [RTSP] [conn 192.168.11.1:55529] opened
2025/08/01 13:53:43 INF [RTSP] [session 13750b71] created by 192.168.11.1:55529
2025/08/01 13:53:43 INF [RTSP] [session 13750b71] is reading from path 'live', with UDP, 1 track (H264)
2025/08/01 13:53:49 INF [RTSP] [session edd9c9fc] destroyed: torn down by 192.168.11.128:56752
2025/08/01 13:53:49 INF [RTSP] [conn 192.168.11.128:56752] closed: read tcp 192.168.11.128:8554->192.168.11.128:56752: read: connection reset by peer
2025/08/01 13:53:49 INF [RTSP] [conn 192.168.11.1:55529] closed: terminated
2025/08/01 13:53:49 INF [RTSP] [session 13750b71] destroyed: terminated
